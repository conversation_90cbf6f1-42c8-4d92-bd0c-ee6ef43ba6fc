using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace THLTW_B2.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [Display(Name = "Họ và tên")]
        public string FullName { get; set; }

        [Display(Name = "Đ<PERSON><PERSON> chỉ")]
        public string? Address { get; set; }

        [Display(Name = "Số điện thoại")]
        public override string? PhoneNumber { get; set; }
    }
}
