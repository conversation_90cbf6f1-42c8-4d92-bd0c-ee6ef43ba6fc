﻿@model THLTW_B2.Models.Product 
@{
    ViewData["Title"] = "Delete Product";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<h2>Edit Product</h2> 

<form asp-action="Update"> 
    <input type="hidden" asp-for="Id" /> 
    <div class="form-group"> 
        <label asp-for="Name"></label> 
        <input asp-for="Name" class="form-control" /> 
        <span asp-validation-for="Name" class="text-danger"></span> 
    </div> 
    <div class="form-group"> 
        <label asp-for="Price"></label> 
        <input asp-for="Price" class="form-control" /> 
        <span asp-validation-for="Price" class="text-danger"></span> 
    </div> 
    <div class="form-group"> 
        <label asp-for="Description"></label> 
        <textarea asp-for="Description" class="form
control"></textarea> 
        <span asp-validation-for="Description" class="text
danger"></span> 
    </div> 
    <button type="submit" class="btn btn-primary">Update</button> 
</form>  