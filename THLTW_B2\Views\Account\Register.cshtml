@model THLTW_B2.Models.RegisterViewModel
@{
    ViewData["Title"] = "Đăng ký";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="mb-3">
                            <label asp-for="UserName" class="form-label"></label>
                            <input asp-for="UserName" class="form-control" />
                            <span asp-validation-for="UserName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="FullName" class="form-label"></label>
                            <input asp-for="FullName" class="form-control" />
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label"></label>
                            <input asp-for="PhoneNumber" class="form-control" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Đăng ký</button>
                            <a asp-action="Login" class="btn btn-outline-secondary">Đã có tài khoản? Đăng nhập</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
