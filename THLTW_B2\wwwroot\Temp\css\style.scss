$white: #ffffff;
$black: #000000;
$primary1: #fbac2e;
$btnColor: #373636;

@mixin main-font {
  font-family: "poppins",
    sans-serif;
}

@mixin secondary-font {
  font-family: "Raleway",
    sans-serif;
}

@mixin hero_btn($col1,
  $col2,
  $pad1,
  $pad2,
  $bRadius) {
  display: inline-block;
  padding: $pad1 $pad2;
  background-color: $col1;
  color: $col2;
  border: 1.5px solid $col1;
  border-radius: $bRadius;

  &:hover {
    background-color: transparent;
    color: $col1;
  }
}

@mixin upperBold {
  text-transform: uppercase;
  font-weight: bold;
}

body {
  @include main-font;
  color: #101010;
  background-color: #ffffff;
}

.layout_padding {
  padding-top: 120px;
  padding-bottom: 120px;
}

.layout_padding2 {
  padding-top: 45px;
  padding-bottom: 45px;
}

.layout_padding2-top {
  padding-top: 45px;
}

.layout_padding2-bottom {
  padding-bottom: 45px;
}

.layout_padding-top {
  padding-top: 120px;
}

.layout_padding-bottom {
  padding-bottom: 120px;
}

.heading_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;

  h2 {
    text-transform: uppercase;
    font-weight: bold;

    span {
      color: $primary1;
    }
  }
}


/*header section*/
.hero_area {}

.sub_page {}

.hero_area.sub_pages {
  height: auto;
}

.header_section {}

.header_section .container-fluid {
  padding-right: 25px;
  padding-left: 25px;
}

.header_section .nav_container {
  margin: 0 auto;
}

.custom_nav-container.navbar-expand-lg .navbar-nav {
  .nav-item {
    .nav-link {
      padding: 10px 30px;
      margin: 0 15px;
      color: #514f4f;
      text-align: center;
      text-transform: uppercase;
      border-radius: 5px;
    }

    &.active,
    &:hover {
      .nav-link {
        background-color: #1b1b1b;
        color: $white;
      }
    }
  }
}

a,
a:hover,
a:focus {
  text-decoration: none;
}

a:hover,
a:focus {
  color: initial;
}

.btn,
.btn:focus {
  outline: none !important;
  box-shadow: none;
}

.navbar-brand,
.navbar-brand:hover {
  @include upperBold;
  font-size: 24px;
  color: #fafcfd;
}

.custom_nav-container .nav_search-btn {
  background-image: url(../images/search-icon.png);
  background-size: 20px;
  background-repeat: no-repeat;
  background-position-y: 7px;
  width: 35px;
  height: 35px;
  padding: 0;
  border: none;
}

.navbar-brand {
  display: flex;
  align-items: center;
  flex-direction: column;
  align-items: center;
  margin-right: 0;
  margin-bottom: 15px;

  img {
    width: 100px;
  }
}

.custom_nav-container {
  z-index: 99999;
  padding: 15px 0;
  flex-direction: column;
  align-items: center;

  .navbar-toggler {
    outline: none;
  }
}

.navbar-toggler .navbar-toggler-icon {
  background-image: url(../images/menu.png);
  background-size: 35px;
}

/*end header section*/

// slider section
.slider_section {
  padding: 0 5%;
  margin-top: 20px;

  .slider_container {
    background-image: url(../images/slider-bg.jpg);
    background-size: cover;
    color: $white;
    padding: 25px 0;
  }

  .row {
    align-items: center;
  }

  .img-box {
    img {
      width: 100%;
    }
  }

  .detail-box {
    width: 75%;
    padding-left: 45px;

    h1 {
      font-weight: bold;
      font-size: 4rem;
      text-transform: uppercase;
    }

    a {
      @include hero_btn($white, $black, 10px, 45px, 5px);
      margin-top: 25px;
      text-transform: uppercase;
    }
  }

  .carousel-indicators {
    margin: 0;
    width: auto;
    justify-content: flex-end;
    bottom: initial;
    top: 0;
    left: initial;
    right: -25px;

    li {
      text-indent: 0;
      background-color: transparent;
      opacity: 1;
      border: none;
      font-size: 7rem;
      font-weight: bold;
      width: auto;
      height: auto;
      display: none;
      color: #1b1b1b;

      &.active {
        display: block;
      }
    }
  }

  .carousel_btn-box {
    display: flex;
    background-color: $white;
    align-items: center;
    position: absolute;
    bottom: -25px;
    right: 10%;
    padding: 7px 10px;
    border-radius: 5px 5px 0 0;

    img {
      margin: 0 10px;
    }

    .carousel-control-prev,
    .carousel-control-next {
      position: unset;
      height: 25px;
      width: 25px;
      background-repeat: no-repeat;
      opacity: 1;
      background-position: center;
    }

    .carousel-control-prev {
      background-image: url(../images/prev.png);
    }

    .carousel-control-next {
      background-image: url(../images/next.png);
    }
  }
}

// end slider section

// service section

.service_section {
  background-image: url(../images/service-bg.jpg);
  background-position: center;
  background-repeat: no-repeat;
  background-size: 85% 85%;

  .heading_container {
    margin-bottom: 35px;
  }

  .img-container {
    padding: 20px 0 20px 20px;
    background: linear-gradient(to right, $white 60%, transparent 60%);

    .img-box {


      img {
        width: 100%;
      }
    }
  }

  .row {
    align-items: center;
  }

  .detail-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    border: none;

    .detail-box {
      background-color: $btnColor;
      color: $white;
      text-align: center;
      text-transform: uppercase;
      min-width: 200px;
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 15px;

      h4 {
        margin: 0;
      }

      &:hover,
      &.active {
        background-color: $primary1;
        cursor: pointer;
      }

    }
  }

  .btn-box {
    display: flex;
    justify-content: center;
    margin-top: 45px;

    a {
      @include hero_btn($btnColor, $white, 10px, 45px, 5px);
    }
  }

}

// end service section

// portfolio section

.portfolio_section {

  .portfolio_container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #2f2f2f;
    margin: 45px 45px 0;

    .img-box {
      margin: 10px;
      position: relative;
      display: flex;
      flex-grow: 1;

      img {
        width: 100%;
        height: 100%;
      }

      .btn-box {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;

        a {
          display: none;
          width: 50px;
          height: 50px;
          background-color: $btnColor;
          border-radius: 100%;
          background-size: 16px;
          background-position: center;
          background-repeat: no-repeat;
          margin: 0 -3px;


        }

        .btn-1 {
          background-image: url(../images/link.png);
        }

        .btn-2 {
          background-image: url(../images/search.png);
        }


      }

      &::before {
        content: "";
        display: none;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgba($color: #ffcc29, $alpha: .85);
        z-index: 0;
      }

      &:hover {
        box-shadow: 0 0 25px 0 rgba($color: #000000, $alpha: .8);

        a {
          display: block;
        }

        &::before {
          display: block;
        }
      }
    }

    .box-1,
    .box-2 {
      display: flex;
    }

    .box-1 {
      padding-right: 10%;
    }

    .box-2 {
      padding-left: 10%;
    }

  }
}

// end portfolio section

// logo section

.logo_section {
  .logo_container {
    box-shadow: 0 0 35px 0 rgba($color: #000000, $alpha: .15);
    margin: 55px 45px 0 45px;
    overflow: hidden;
    position: relative;
  }

  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0 10px;
    margin: 100px 0;

    .img-box {
      img {
        width: 100%;
      }
    }

    .detail-box {
      h4 {
        text-transform: uppercase;
        font-weight: bold;
        color: #363636;
      }
    }



    &.b1 {
      animation: odd-box-animate 7s infinite;
    }

    &.b2 {
      animation: even-box-animate 7s infinite;
    }

    @keyframes odd-box-animate {
      0% {
        transform: translateY(90px);
      }

      50% {
        transform: translateY(-90px);
      }

      100% {
        transform: translateY(90px);
      }
    }

    @keyframes even-box-animate {
      0% {
        transform: translateY(-90px);
      }

      50% {
        transform: translateY(90px);
      }

      100% {
        transform: translateY(-90px);
      }
    }

  }


  .owl-carousel {
    position: unset;
    width: 90%;
    margin: auto;

    .owl-nav {

      .owl-prev,
      .owl-next {
        background-color: $black;
        width: 65px;
        height: 75px;
        outline: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-repeat: no-repeat;
        background-size: 15px;
      }

      .owl-prev {
        left: -30px;
        background-color: $black;
        border-radius: 0 100% 100% 0;
        background-image: url(../images/prev-arrow.png);
        background-position: 37px center;
      }

      .owl-next {
        right: -30px;
        border-radius: 100% 0 0 100%;
        background-image: url(../images/next-arrow.png);
        background-position: 12.8px center;
      }
    }

    .owl-dots {
      display: none;
    }
  }
}

// end logo section

// started section

.started_section {

  .row {
    align-items: center;
  }

  .heading_container {
    align-items: flex-start;
    text-align: left;

    h2 {
      font-size: 2.5rem;
    }
  }

  .btn-box {
    display: flex;
    justify-content: flex-end;

    a {
      @include hero_btn($btnColor, $white, 14px, 75px, 5px);
      text-transform: uppercase;
    }
  }
}

// end started section

// agency section

.agency_section {

  .agency_container {
    background-image: url(../images/agency-img.jpg);
    background-size: cover;
    margin: 0 45px;
    padding: 90px 75px;

    .box {
      background-color: rgba($color: #000000, $alpha: .8);
      color: $white;
      width: 60%;
      padding: 120px 90px;

      .detail-box {
        .heading_container {
          align-items: flex-start;
          text-align: left;
        }

        p {
          margin-top: 35px;
        }

        a {
          @include hero_btn($white, $black, 10px, 45px, 5px);
          margin-top: 45px;
        }
      }
    }
  }

}

// end agency section

// contact section
.contact_section {
  position: relative;


  .container-bg {
    box-shadow: 0 0 25px 0 rgba($color: #000000, $alpha: .1);

  }

  .heading_container {
    align-items: flex-start;
    text-align: left;
    margin-bottom: 45px;
  }

  .row {
    align-items: stretch;
  }

  form {
    padding-right: 35px;
    padding: 45px 20px;
  }

  input {
    width: 100%;
    border: 1px solid #919191;
    height: 50px;
    margin-bottom: 25px;
    padding-left: 25px;
    background-color: transparent;
    outline: none;
    color: #101010;


    &::placeholder {
      color: #131313;
    }

    &.message-box {
      height: 120px;
    }
  }

  button {
    @include hero_btn($primary1, $white, 12px, 45px, 0);
    display: block;
    color: #fff;
    margin: 35px auto 0 auto;
  }

  .map_container {
    height: 100%;


    .map-responsive {
      height: 100%;
    }
  }
}

// end contact section

// client section

.client_section {
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 700px;
    margin: auto;
    margin-top: 45px;

    .client_id {
      display: flex;
      padding: 15px;
      align-items: center;
      position: relative;
      box-shadow: 0 0 25px 0 rgba($color: #000000, $alpha: .1);

      .name {
        margin-right: 15px;

        h4 {
          margin: 0;
          font-weight: bold;
          color: #424242;
        }
      }

      .img-box {
        width: 150px;
        min-width: 150px;

        img {
          width: 100%;
        }
      }

      &::before {
        content: "";
        position: absolute;
        bottom: 0;
        right: 0;
        width: 63px;
        height: 31px;
        clip-path: polygon(0 0, 0% 100%, 100% 0);
        background-color: $primary1;
        z-index: -1;
        transform: translateY(100%);
      }

    }

    .detail-box {
      text-align: center;
      margin-top: 55px;

      img {
        margin-top: 25px;
      }
    }

  }


}

// end client section



/* info section */
.info_section {
  position: relative;
  background-color: #2d2d2d;
  color: $white;
  margin: 0 45px 45px;

  .social_container {
    justify-content: center;
    margin-top: 25px;
    margin-bottom: 30px;

    .social_box {
      display: flex;
      align-items: center;
      justify-content: center;

      a {
        margin: 0 10px;
      }
    }
  }

  .row>div {
    margin-top: 25px;
  }

  a {
    text-transform: none;
  }


  h6 {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 18px;
    margin-bottom: 25px;
  }


  p {
    color: #cbc9c9;
  }



  .info_link-box {
    a {
      display: flex;
      align-items: center;
      margin: 15px 0;


      &:hover {
        color: $white;
      }

      img {
        width: 50px;
        margin-right: 15px;
      }

      span {
        color: #cbc9c9;
      }

      &:hover {
        span {
          color: $white;
        }
      }
    }
  }


}

/* end info section */


/* footer section*/

.footer_section {
  margin-top: 45px;
  font-weight: 500;
}

.footer_section p {
  padding: 20px 0;
  margin: 0 auto;
  text-align: center;
  border-top: 1.5px solid $primary1;
  width: 80%;
}

.footer_section a {
  color: #cbc9c9;
}

/* end footer section*/