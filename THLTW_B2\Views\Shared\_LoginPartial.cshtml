<ul class="navbar-nav">
@if (User.Identity.IsAuthenticated)
{
    <li class="nav-item">
        <a class="nav-link text-dark" asp-controller="Account" asp-action="Profile" title="Thông tin cá nhân"><PERSON><PERSON><PERSON> @User.Identity.Name!</a>
    </li>
    <li class="nav-item">
        <a class="nav-link text-dark" asp-controller="Account" asp-action="Profile" title="Thông tin cá nhân">Thông tin cá nhân</a>
    </li>
    <li class="nav-item">
        <form id="logoutForm" class="form-inline" asp-controller="Account" asp-action="Logout" method="post">
            <button id="logout" type="submit" class="nav-link btn btn-link text-dark border-0">Đăng xuất</button>
        </form>
    </li>
}
else
{
    <li class="nav-item">
        <a class="nav-link text-dark" id="register" asp-controller="Account" asp-action="Register"><PERSON><PERSON><PERSON> ký</a>
    </li>
    <li class="nav-item">
        <a class="nav-link text-dark" id="login" asp-controller="Account" asp-action="Login">Đăng nhập</a>
    </li>
}
</ul>
