@if (User.Identity.IsAuthenticated)
{
    <div class="dropdown">
        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-user me-2"></i>@User.Identity.Name
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
            <li>
                <a class="dropdown-item" asp-controller="Account" asp-action="Profile">
                    <i class="fas fa-user-circle me-2"></i>Thông tin cá nhân
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                    </button>
                </form>
            </li>
        </ul>
    </div>
}
else
{
    <div class="d-flex gap-2">
        <a asp-controller="Account" asp-action="Login" class="btn btn-outline-light">
            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
        </a>
        <a asp-controller="Account" asp-action="Register" class="btn btn-light">
            <i class="fas fa-user-plus me-2"></i>Đăng ký
        </a>
    </div>
}
