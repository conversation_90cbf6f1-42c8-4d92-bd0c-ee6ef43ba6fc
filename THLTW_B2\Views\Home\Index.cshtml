﻿@{
    ViewData["Title"] = "Trang chủ";
}

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">Chào mừng đến với Cửa hàng Online</h1>
                <p class="lead mb-4">Khám phá hàng ngàn sản phẩm chất lượng cao với giá cả hợp lý. Mua sắm dễ dàng, giao hàng nhanh chóng!</p>
                <div class="d-flex gap-3">
                    <a asp-controller="Product" asp-action="Index" class="btn btn-light btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Mua sắm ngay
                    </a>
                    <a asp-controller="Home" asp-action="About" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-info-circle me-2"></i>Tìm hiểu thêm
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <img src="https://via.placeholder.com/500x400/667eea/ffffff?text=Sản+phẩm+chất+lượng"
                     alt="Hero Image" class="img-fluid rounded shadow">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="fw-bold">Tại sao chọn chúng tôi?</h2>
                <p class="text-muted">Những lý do khiến khách hàng tin tưởng và lựa chọn</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="text-center p-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-shipping-fast fa-2x"></i>
                    </div>
                    <h5>Giao hàng nhanh</h5>
                    <p class="text-muted">Giao hàng trong 24h với đội ngũ vận chuyển chuyên nghiệp</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-4">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                    <h5>Bảo hành chất lượng</h5>
                    <p class="text-muted">Cam kết chất lượng với chính sách bảo hành và đổi trả linh hoạt</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center p-4">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-headset fa-2x"></i>
                    </div>
                    <h5>Hỗ trợ 24/7</h5>
                    <p class="text-muted">Đội ngũ chăm sóc khách hàng luôn sẵn sàng hỗ trợ bạn</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="bg-light py-5">
    <div class="container text-center">
        <h3 class="fw-bold mb-3">Sẵn sàng mua sắm?</h3>
        <p class="mb-4">Khám phá hàng ngàn sản phẩm đang chờ bạn</p>
        <a asp-controller="Product" asp-action="Index" class="btn btn-primary btn-lg">
            <i class="fas fa-arrow-right me-2"></i>Xem tất cả sản phẩm
        </a>
    </div>
</section>
