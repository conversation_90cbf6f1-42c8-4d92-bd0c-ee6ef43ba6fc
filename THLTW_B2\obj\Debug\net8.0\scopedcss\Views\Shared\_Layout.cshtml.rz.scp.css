/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-z786mqxdjq] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-z786mqxdjq] {
  color: #0077cc;
}

.btn-primary[b-z786mqxdjq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-z786mqxdjq], .nav-pills .show > .nav-link[b-z786mqxdjq] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-z786mqxdjq] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-z786mqxdjq] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-z786mqxdjq] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-z786mqxdjq] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-z786mqxdjq] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
