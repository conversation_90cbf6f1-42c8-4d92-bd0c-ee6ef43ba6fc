namespace THLTW_B2.Models
{
    public enum UserRole
    {
        User = 0,
        Admin = 1
    }

    public static class UserRoleExtensions
    {
        public static string GetDisplayName(this UserRole role)
        {
            return role switch
            {
                UserRole.Admin => "Quản trị viên",
                UserRole.User => "Người dùng",
                _ => "Không xác định"
            };
        }

        public static string GetRoleName(this UserRole role)
        {
            return role.ToString();
        }
    }
}
