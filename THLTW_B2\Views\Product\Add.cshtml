﻿@model THLTW_B2.Models.Product 
@using Microsoft.AspNetCore.Mvc.Rendering 
  
<h1>Add Product</h1> 
 
<form asp-action="Add"> 
    <div asp-validation-summary="All" class="text-danger"></div> 
    <div class="form-group"> 
        <label asp-for="Name"></label> 
        <input asp-for="Name" class="form-control" /> 
        <span asp-validation-for="Name" class="text-danger"></span> 
    </div> 
    <div class="form-group"> 
        <label asp-for="Price"></label> 
        <input asp-for="Price" class="form-control" /> 
        <span asp-validation-for="Price" class="text-danger"></span> 
    </div> 
    <div class="form-group"> 
        <label asp-for="Description"></label> 
        <textarea asp-for="Description" class="form
control"></textarea> 
        <span asp-validation-for="Description" class="text
danger"></span> 
    </div> 
    <div class="form-group">
        <label asp-for="CategoryId">Category</label>
        <select asp-for="CategoryId" asp-items="ViewBag.Categories"
                class="form-control"></select>
    </div>
    <button type="submit" class="btn btn-primary">Add</button>
</form>
