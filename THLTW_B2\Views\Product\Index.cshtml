﻿@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Delete Product";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<h2>Products</h2>

<table class="table">
    <thead>
        <tr>
            <th>Name</th>
            <th>Price</th>
            <th>Description</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var product in Model)
        {
            <tr>
                <td>@product.Name</td>
                <td>@product.Price</td>
                <td>@product.Description</td>
                <td>
                    <a asp-action="Display" asp-route-id="@product.Id">View</a> |
                    <a asp-action="Update" asp-route-id="@product.Id">Edit</a> |
                    <a asp-action="Delete" asp-route-id="@product.Id">Delete</a>
                </td>
            </tr>
        }
    </tbody>
</table>
