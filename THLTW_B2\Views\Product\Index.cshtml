﻿@model IEnumerable<THLTW_B2.Models.Product>
@{
    ViewData["Title"] = "Sản phẩm";
}

<div class="container py-5">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="fw-bold">
                <i class="fas fa-box me-2 text-primary"></i><PERSON><PERSON> sách sản phẩm
            </h1>
            <p class="text-muted">Khám phá các sản phẩm chất lượng cao</p>
        </div>
        <div class="col-md-4 text-end">
            @if (User.Identity.IsAuthenticated)
            {
                <a asp-action="Add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Thêm sản phẩm
                </a>
            }
        </div>
    </div>

    <!-- Products Grid -->
    @if (Model != null && Model.Any())
    {
        <div class="row g-4">
            @foreach (var product in Model)
            {
                <div class="col-lg-4 col-md-6">
                    <div class="card product-card h-100">
                        <div class="position-relative">
                            <img src="@(string.IsNullOrEmpty(product.ImageUrl) ? "https://via.placeholder.com/300x200/e9ecef/6c757d?text=Sản+phẩm" : product.ImageUrl)"
                                 class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-primary">@product.Category?.Name</span>
                            </div>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text text-muted flex-grow-1">@product.Description</p>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <span class="h5 text-primary mb-0">@product.Price.ToString("C0", new System.Globalization.CultureInfo("vi-VN"))</span>
                                <div class="btn-group" role="group">
                                    <a asp-action="Display" asp-route-id="@product.Id" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if (User.Identity.IsAuthenticated)
                                    {
                                        <a asp-action="Update" asp-route-id="@product.Id" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@product.Id" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="text-center py-5">
            <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
            <h3 class="text-muted">Chưa có sản phẩm nào</h3>
            <p class="text-muted">Hãy thêm sản phẩm đầu tiên của bạn!</p>
            @if (User.Identity.IsAuthenticated)
            {
                <a asp-action="Add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Thêm sản phẩm đầu tiên
                </a>
            }
        </div>
    }
</div>
