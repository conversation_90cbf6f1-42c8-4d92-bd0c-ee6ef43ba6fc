using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using THLTW_B2.DataAccess;
using THLTW_B2.Models;
using THLTW_B2.Repositories;

var builder = WebApplication.CreateBuilder(args);

// Thêm MVC vào dự án
builder.Services.AddControllersWithViews();

// Cấu hình kết nối Database
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Cấu hình ASP.NET Core Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>()
        .AddDefaultTokenProviders()
        .AddDefaultUI()
        .AddEntityFrameworkStores<ApplicationDbContext>();

// Thêm Razor Pages cho Identity UI
builder.Services.AddRazorPages();

// Đ<PERSON>ng ký các repositories
builder.Services.AddScoped<IProductRepository, EFProductRepository>();
builder.Services.AddScoped<ICategoryRepository, EFCategoryRepository>();

var app = builder.Build();

// Middleware pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
}

app.UseStaticFiles();

app.UseRouting();

// Authentication phải trước Authorization
app.UseAuthentication();
app.UseAuthorization();

// Map Razor Pages cho Identity UI
app.MapRazorPages();

// Cấu hình routing mặc định
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
